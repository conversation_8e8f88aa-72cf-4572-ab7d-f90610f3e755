/**
 * 用户点击功能测试
 * 测试发送者点击跳转到用户页面的功能
 */

import { describe, it, expect, jest } from '@jest/globals';

// 模拟 Next.js 路由
const mockPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: mockPush,
  }),
  useParams: () => ({
    username: 'testuser'
  })
}));

// 测试数据
const mockPin = {
  id: '1',
  content: '测试消息内容',
  contentType: 'text' as const,
  contentElements: [],
  timestamp: '2024-01-01T00:00:00Z',
  setter: {
    name: '设置者',
    avatar: 'setter.jpg'
  },
  sender: {
    name: '发送者',
    avatar: 'sender.jpg'
  },
  groupName: '测试群组'
};

describe('用户点击功能', () => {
  beforeEach(() => {
    mockPush.mockClear();
  });

  it('应该正确编码用户名并跳转到用户页面', () => {
    // 测试用户名编码
    const username = '测试用户 123';
    const encodedUsername = encodeURIComponent(username);
    const expectedUrl = `/user/${encodedUsername}`;
    
    expect(encodedUsername).toBe('%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7%20123');
    expect(expectedUrl).toBe('/user/%E6%B5%8B%E8%AF%95%E7%94%A8%E6%88%B7%20123');
  });

  it('应该正确处理特殊字符的用户名', () => {
    const specialUsernames = [
      '用户@123',
      '用户#456',
      '用户&789',
      '用户 with spaces'
    ];

    specialUsernames.forEach(username => {
      const encoded = encodeURIComponent(username);
      const decoded = decodeURIComponent(encoded);
      expect(decoded).toBe(username);
    });
  });
});

describe('用户页面功能', () => {
  it('应该正确筛选用户消息', () => {
    const allPins = [
      { ...mockPin, id: '1', sender: { name: '用户A', avatar: 'a.jpg' } },
      { ...mockPin, id: '2', sender: { name: '用户B', avatar: 'b.jpg' } },
      { ...mockPin, id: '3', sender: { name: '用户A', avatar: 'a.jpg' } },
    ];

    const targetUser = '用户A';
    const filteredPins = allPins.filter(pin => pin.sender.name === targetUser);
    
    expect(filteredPins).toHaveLength(2);
    expect(filteredPins.every(pin => pin.sender.name === targetUser)).toBe(true);
  });

  it('应该正确计算用户统计信息', () => {
    const userPins = [
      { ...mockPin, id: '1', contentType: 'text' as const },
      { ...mockPin, id: '2', contentType: 'image' as const },
      { ...mockPin, id: '3', contentType: 'mixed' as const },
      { ...mockPin, id: '4', contentType: 'text' as const },
    ];

    const textPins = userPins.filter(pin => pin.contentType === 'text').length;
    const imagePins = userPins.filter(pin => pin.contentType === 'image' || pin.contentType === 'gif').length;
    const mixedPins = userPins.filter(pin => pin.contentType === 'mixed').length;

    expect(textPins).toBe(2);
    expect(imagePins).toBe(1);
    expect(mixedPins).toBe(1);
    expect(userPins.length).toBe(4);
  });
});

// 导出测试工具函数
export const testUtils = {
  createMockPin: (overrides = {}) => ({
    ...mockPin,
    ...overrides
  }),
  
  createMockPins: (count: number, senderName: string) => {
    return Array.from({ length: count }, (_, index) => ({
      ...mockPin,
      id: `${index + 1}`,
      sender: { name: senderName, avatar: `${senderName}.jpg` }
    }));
  }
};
