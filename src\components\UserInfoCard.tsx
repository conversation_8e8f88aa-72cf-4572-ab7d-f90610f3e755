'use client';

import { useMemo } from 'react';
import SmartAvatar from './SmartAvatar';
import { PinData } from './PinCard';
import { formatTime } from '@/utils/dateUtils';

interface UserInfoCardProps {
  username: string;
  userPins: PinData[];
  onBackToHome: () => void;
}

export default function UserInfoCard({ username, userPins, onBackToHome }: UserInfoCardProps) {
  // 计算用户统计信息
  const userStats = useMemo(() => {
    if (userPins.length === 0) {
      return {
        totalPins: 0,
        textPins: 0,
        imagePins: 0,
        mixedPins: 0,
        latestPin: null,
        oldestPin: null
      };
    }

    const textPins = userPins.filter(pin => pin.contentType === 'text').length;
    const imagePins = userPins.filter(pin => pin.contentType === 'image' || pin.contentType === 'gif').length;
    const mixedPins = userPins.filter(pin => pin.contentType === 'mixed').length;

    // 按时间排序找到最新和最旧的消息
    const sortedPins = [...userPins].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
    const latestPin = sortedPins[0];
    const oldestPin = sortedPins[sortedPins.length - 1];

    return {
      totalPins: userPins.length,
      textPins,
      imagePins,
      mixedPins,
      latestPin,
      oldestPin
    };
  }, [userPins]);

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <SmartAvatar
            nickname={username}
            size={64}
            className="flex-shrink-0"
          />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">{username}</h1>
            <p className="text-gray-600 mt-1">
              共 {userStats.totalPins} 条精华消息
            </p>
          </div>
        </div>
        
        <button
          onClick={onBackToHome}
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200"
        >
          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回首页
        </button>
      </div>

      {/* 统计信息 */}
      {userStats.totalPins > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-blue-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{userStats.totalPins}</div>
            <div className="text-sm text-blue-800">总消息数</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{userStats.textPins}</div>
            <div className="text-sm text-green-800">文字消息</div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{userStats.imagePins}</div>
            <div className="text-sm text-purple-800">图片消息</div>
          </div>
          <div className="bg-orange-50 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{userStats.mixedPins}</div>
            <div className="text-sm text-orange-800">混合消息</div>
          </div>
        </div>
      )}

      {/* 时间信息 */}
      {userStats.latestPin && userStats.oldestPin && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-600">
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>最新消息：{formatTime(userStats.latestPin.timestamp)}</span>
          </div>
          <div className="flex items-center space-x-2">
            <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <span>最早消息：{formatTime(userStats.oldestPin.timestamp)}</span>
          </div>
        </div>
      )}
    </div>
  );
}
